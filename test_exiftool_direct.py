#!/usr/bin/env python3
"""
Direct ExifTool test script
"""

import os
import subprocess
import shutil

def main():
    print("Direct ExifTool Test")
    print("=" * 30)
    
    # Find ExifTool
    exiftool_path = os.path.join(os.path.dirname(__file__), 'exiftool-13.32_64', 'exiftool.exe')
    
    if not os.path.exists(exiftool_path):
        exiftool_path = os.path.join(os.path.dirname(__file__), 'exiftool-13.32_64', 'exiftool(-k).exe')
    
    if not os.path.exists(exiftool_path):
        print(f"ExifTool not found at: {exiftool_path}")
        return
    
    print(f"ExifTool found: {exiftool_path}")
    
    # Test version
    try:
        result = subprocess.run([exiftool_path, '-ver'], capture_output=True, text=True)
        print(f"ExifTool version: {result.stdout.strip()}")
    except Exception as e:
        print(f"Error getting version: {e}")
        return
    
    # Get test file
    file_path = input("\nEnter path to test file: ").strip().strip('"')
    
    if not os.path.exists(file_path):
        print("File not found!")
        return
    
    print(f"Test file: {file_path}")
    
    # Create backup
    backup_path = file_path + ".backup"
    shutil.copy2(file_path, backup_path)
    print(f"Backup created: {backup_path}")
    
    # Read existing metadata
    print("\n=== Reading existing metadata ===")
    try:
        read_cmd = [exiftool_path, '-j', file_path]
        result = subprocess.run(read_cmd, capture_output=True, text=True)
        if result.returncode == 0:
            print("Existing metadata (JSON):")
            print(result.stdout[:500] + "..." if len(result.stdout) > 500 else result.stdout)
        else:
            print(f"Error reading: {result.stderr}")
    except Exception as e:
        print(f"Error: {e}")
    
    # Write test metadata
    print("\n=== Writing test metadata ===")
    try:
        write_cmd = [
            exiftool_path,
            '-overwrite_original',
            '-Title=Test Title from Gema',
            '-Artist=Test Artist from Gema', 
            '-Comment=Test Comment from Gema',
            '-XMP:CustomField=Test Custom Field',
            file_path
        ]
        
        print(f"Command: {' '.join(write_cmd)}")
        result = subprocess.run(write_cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✓ Write successful!")
            print(result.stdout)
        else:
            print(f"✗ Write failed: {result.stderr}")
    except Exception as e:
        print(f"Error: {e}")
    
    # Verify written metadata
    print("\n=== Verifying written metadata ===")
    try:
        verify_cmd = [exiftool_path, '-Title', '-Artist', '-Comment', '-XMP:CustomField', file_path]
        result = subprocess.run(verify_cmd, capture_output=True, text=True)
        if result.returncode == 0:
            print("Verification result:")
            print(result.stdout)
        else:
            print(f"Error verifying: {result.stderr}")
    except Exception as e:
        print(f"Error: {e}")
    
    # Ask to restore backup
    restore = input("\nRestore backup? (y/n): ").lower().startswith('y')
    if restore:
        shutil.move(backup_path, file_path)
        print("✓ Backup restored")
    else:
        os.remove(backup_path)
        print("✓ Backup deleted, changes kept")

if __name__ == "__main__":
    main()
    input("\nPress Enter to exit...")
