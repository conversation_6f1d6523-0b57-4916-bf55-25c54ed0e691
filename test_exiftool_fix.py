#!/usr/bin/env python3
"""
Test and fix ExifTool issues
"""

import os
import subprocess
import sys

def test_exiftool_executable(exe_path):
    """Test if ExifTool executable works"""
    print(f"Testing: {exe_path}")
    
    if not os.path.exists(exe_path):
        print(f"❌ File not found: {exe_path}")
        return False
    
    try:
        # Test version command
        result = subprocess.run([exe_path, '-ver'], 
                              capture_output=True, 
                              text=True, 
                              timeout=10)
        
        if result.returncode == 0:
            print(f"✅ ExifTool version: {result.stdout.strip()}")
            return True
        else:
            print(f"❌ Error: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ Timeout - ExifTool took too long to respond")
        return False
    except FileNotFoundError:
        print("❌ File not found or not executable")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def main():
    print("ExifTool Diagnostic Tool")
    print("=" * 40)
    
    script_dir = os.path.dirname(os.path.abspath(__file__))
    
    # Test different ExifTool paths
    test_paths = [
        os.path.join(script_dir, 'exiftool-13.32_64', 'exiftool.exe'),
        os.path.join(script_dir, 'exiftool-13.32_64', 'exiftool(-k).exe'),
    ]
    
    working_path = None
    
    for path in test_paths:
        print(f"\n--- Testing {os.path.basename(path)} ---")
        if test_exiftool_executable(path):
            working_path = path
            break
    
    if working_path:
        print(f"\n✅ Working ExifTool found: {working_path}")
        
        # Test with a simple command
        print("\n--- Testing metadata read ---")
        try:
            # Create a test file if needed
            test_file = os.path.join(script_dir, 'test_image.txt')
            if not os.path.exists(test_file):
                with open(test_file, 'w') as f:
                    f.write("Test file for ExifTool")
            
            result = subprocess.run([working_path, test_file], 
                                  capture_output=True, 
                                  text=True, 
                                  timeout=10)
            
            if result.returncode == 0:
                print("✅ ExifTool can read files successfully")
            else:
                print(f"⚠️ Warning: {result.stderr}")
                
        except Exception as e:
            print(f"⚠️ Test read error: {e}")
    
    else:
        print("\n❌ No working ExifTool found!")
        print("\nTroubleshooting steps:")
        print("1. Check if exiftool-13.32_64 folder exists")
        print("2. Check if exiftool.exe or exiftool(-k).exe exists")
        print("3. Try running as administrator")
        print("4. Check antivirus software blocking execution")
    
    print("\n--- Environment Info ---")
    print(f"Python: {sys.version}")
    print(f"Working directory: {os.getcwd()}")
    print(f"Script directory: {script_dir}")
    
    input("\nPress Enter to exit...")

if __name__ == "__main__":
    main()
