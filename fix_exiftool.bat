@echo off
echo Fixing ExifTool installation...
echo.

REM Check if exiftool(-k).exe exists
if exist "exiftool-13.32_64\exiftool(-k).exe" (
    echo Found exiftool(-k).exe
    
    REM Copy to exiftool.exe if it doesn't exist
    if not exist "exiftool-13.32_64\exiftool.exe" (
        echo Creating exiftool.exe...
        copy "exiftool-13.32_64\exiftool(-k).exe" "exiftool-13.32_64\exiftool.exe"
        if errorlevel 1 (
            echo Error: Failed to copy file
            echo Please run as administrator or copy manually
            pause
            exit /b 1
        )
        echo ✓ Successfully created exiftool.exe
    ) else (
        echo ✓ exiftool.exe already exists
    )
    
    REM Test ExifTool
    echo Testing ExifTool...
    "exiftool-13.32_64\exiftool.exe" -ver
    if errorlevel 1 (
        echo Error: ExifTool test failed
        pause
        exit /b 1
    )
    echo ✓ ExifTool is working correctly
    
) else (
    echo Error: exiftool(-k).exe not found
    echo Please ensure ExifTool is properly extracted to exiftool-13.32_64\ folder
    pause
    exit /b 1
)

echo.
echo ExifTool fix completed successfully!
pause
