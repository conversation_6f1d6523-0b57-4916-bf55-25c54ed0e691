@echo off
echo Installing ExifTool for Gema...
echo.

REM Check if ExifTool is already installed
exiftool -ver >nul 2>&1
if not errorlevel 1 (
    echo ExifTool is already installed!
    exiftool -ver
    pause
    exit /b 0
)

echo ExifTool not found. Installing...
echo.

REM Create temp directory
set TEMP_DIR=%TEMP%\exiftool_install
mkdir "%TEMP_DIR%" 2>nul

REM Download ExifTool
echo Downloading ExifTool...
powershell -Command "& {Invoke-WebRequest -Uri 'https://exiftool.org/exiftool-12.70.zip' -OutFile '%TEMP_DIR%\exiftool.zip'}"

if not exist "%TEMP_DIR%\exiftool.zip" (
    echo Failed to download ExifTool
    echo Please download manually from: https://exiftool.org/
    pause
    exit /b 1
)

REM Extract ExifTool
echo Extracting ExifTool...
powershell -Command "& {Expand-Archive -Path '%TEMP_DIR%\exiftool.zip' -DestinationPath '%TEMP_DIR%' -Force}"

REM Install to Program Files
set INSTALL_DIR=%ProgramFiles%\ExifTool
echo Installing to %INSTALL_DIR%...

mkdir "%INSTALL_DIR%" 2>nul
copy "%TEMP_DIR%\exiftool(-k).exe" "%INSTALL_DIR%\exiftool.exe"

if not exist "%INSTALL_DIR%\exiftool.exe" (
    echo Failed to install ExifTool
    echo Please run as administrator
    pause
    exit /b 1
)

REM Add to PATH
echo Adding ExifTool to PATH...
setx PATH "%PATH%;%INSTALL_DIR%" /M

REM Cleanup
rmdir /s /q "%TEMP_DIR%"

echo.
echo ExifTool installed successfully!
echo Please restart your command prompt or computer for PATH changes to take effect.
echo.
pause
